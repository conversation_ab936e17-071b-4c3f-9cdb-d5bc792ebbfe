# Error Handling Improvements for Audience API

## Problem Addressed

The audience tab was continuously retrying API calls after receiving 500 errors, causing unnecessary server load and poor user experience.

## Changes Made

### 1. Query Retry Logic Updates

#### Audience Hook (`Audience.hook.ts`)

- **No Retries on HTTP 4xx/5xx**: Prevents automatic retries on client and server errors
- **Limited Retries**: Only retries unknown errors, maximum 1 attempt
- **Network Error Handling**: Doesn't retry on persistent network errors (NETWORK_ERROR, ECONNABORTED)
- **Logging**: Added detailed logging for retry decisions

#### AudienceOverview Hook (`AudienceOverview.hook.ts`)

- **Same Logic**: Applied identical retry logic for consistency
- **Component-Specific Logging**: Distinguishes between Audience and AudienceOverview in logs

### 2. Error Handler Updates (`errorHandler.ts`)

#### Server Error Messages

- **500 Internal Server Error**: Changed from "Server error occurred..." to "Something went wrong on our end..."
- **shouldRetry Flag**: Set to `false` for all server errors (500, 502, 503, 504)
- **User-Friendly Language**: More approachable error messages

### 3. Error Display Component (`ErrorDisplay.tsx`)

#### Visual Improvements

- **"Something Went Wrong" Title**: More user-friendly title for server errors
- **Always Show Retry Button**: Manual retry always available, regardless of shouldRetry flag
- **Button Styling**: Critical errors get primary button styling for emphasis
- **Better Help Text**: "We're experiencing technical difficulties..." for server errors

### 4. Query Configuration Enhancements

#### Background Refetching

- **Disabled Window Focus Refetch**: Prevents automatic refetch when window gains focus
- **Smart Reconnect Logic**: Only refetches on reconnect if last error wasn't a server error
- **Stale Time**: 5-minute cache to reduce unnecessary requests

## Specific Retry Behavior

### Will NOT Retry Automatically:

- ✅ HTTP 400 (Bad Request)
- ✅ HTTP 401 (Unauthorized)
- ✅ HTTP 403 (Forbidden)
- ✅ HTTP 404 (Not Found)
- ✅ HTTP 422 (Unprocessable Entity)
- ✅ HTTP 429 (Rate Limited)
- ✅ HTTP 500 (Internal Server Error)
- ✅ HTTP 502 (Bad Gateway)
- ✅ HTTP 503 (Service Unavailable)
- ✅ HTTP 504 (Gateway Timeout)
- ✅ Network Errors (NETWORK_ERROR, ECONNABORTED)

### Will Retry (Max 1 Attempt):

- ✅ Unknown errors without specific HTTP status
- ✅ Unexpected connection issues

## User Experience Improvements

### Error Messages

- **Clear Titles**: "Something Went Wrong" instead of technical terms
- **Helpful Descriptions**: Contextual guidance for different error types
- **Manual Recovery**: Always available "Try Again" button

### Visual Feedback

- **Appropriate Icons**: Different icons for network, server, and auth errors
- **Color Coding**: Severity-based color scheme (critical = red, high = red, medium = orange)
- **Development Info**: Error codes visible in development mode only

### Retry Mechanism

- **Manual Control**: Users can retry when they choose
- **No Automatic Spam**: Prevents continuous API calls on server errors
- **Smart Caching**: Reduces redundant requests with 5-minute stale time

## Implementation Details

### Query Keys

Both hooks use comprehensive query keys that include:

- Project ID
- Active tab (for Audience)
- Date ranges (primary and comparison)
- Comparison enabled flag

This ensures proper cache invalidation when parameters change.

### Error Propagation

- **Enhanced Error Objects**: Errors include code, severity, shouldRetry, and technical details
- **Contextual Logging**: Different log prefixes for different components
- **Structured Error Handling**: Consistent error processing across all hooks

### Fallback Behavior

- **Partial Data Loading**: Individual endpoint failures don't break entire component
- **Graceful Degradation**: Shows available data even if some endpoints fail
- **Warning Logs**: Alerts developers to missing data without breaking UX

## Testing Scenarios

### Server Error (500)

1. API returns 500 error
2. Query immediately fails (no retries)
3. Error display shows "Something Went Wrong"
4. User can manually retry
5. No automatic background refetching

### Network Error

1. Network connection fails
2. Query immediately fails (no retries)
3. Error display shows "Connection Problem"
4. User can manually retry
5. Smart reconnect logic applies

### Partial Failures

1. Some endpoints succeed, others fail
2. Component shows available data
3. Warnings logged for missing data
4. No error display if any data available

## Benefits

### Performance

- **Reduced Server Load**: No continuous retries on server errors
- **Efficient Caching**: 5-minute stale time reduces redundant requests
- **Smart Refetching**: Only refetches when appropriate

### User Experience

- **Clear Communication**: User-friendly error messages
- **Manual Control**: Users decide when to retry
- **Visual Clarity**: Appropriate icons and colors for different error types

### Developer Experience

- **Detailed Logging**: Clear information about retry decisions
- **Structured Errors**: Consistent error handling patterns
- **Debug Information**: Error codes available in development

## Future Enhancements

1. **Error Analytics**: Track error patterns for monitoring
2. **Offline Support**: Handle offline/online state changes
3. **Progressive Retry**: Exponential backoff for specific error types
4. **User Notifications**: Toast notifications for critical errors
5. **Error Recovery**: Automatic recovery strategies for specific scenarios
