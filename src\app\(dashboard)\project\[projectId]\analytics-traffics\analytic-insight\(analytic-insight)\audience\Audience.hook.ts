import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import http from "@/services/httpService";
import type { AudienceResponse, GA4ApiResponse } from "./Audience.types";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { transformAudienceData, AUDIENCE_TABS } from "./Audience.utils";
import { processApiErrorEnhanced, logError } from "@/utils/errorHandler";

/**
 * Fetches audience data from the GA4 endpoints with date range support.
 *
 * @param activeTab The currently active tab to determine which data to highlight
 * @returns The transformed audience data
 */
const useAudience = (activeTab: string) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "audience",
      projectId,
      activeTab,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<AudienceResponse> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      try {
        // Fetch data for all tabs in parallel with individual error handling
        const primaryPromises = AUDIENCE_TABS.map(async (tab) => {
          try {
            const response = await http.get(`${tab.endpoint}/${projectId}/`, {
              params: {
                start_date: startDate,
                end_date: endDate,
              },
              useAuth: true,
            });
            return {
              key: tab.key,
              data: response.data as GA4ApiResponse,
              error: null,
            };
          } catch (tabError) {
            const errorResult = processApiErrorEnhanced(tabError);
            logError(errorResult, `Audience Primary Data - ${tab.title}`);

            // Return error info instead of throwing to allow partial data loading
            return { key: tab.key, data: null, error: errorResult };
          }
        });

        const primaryResults = await Promise.all(primaryPromises);
        const primaryResponses: Record<string, GA4ApiResponse> = {};
        const primaryErrors: Record<string, any> = {};

        primaryResults.forEach(({ key, data, error }) => {
          if (data) {
            primaryResponses[key] = data;
          } else if (error) {
            primaryErrors[key] = error;
          }
        });

        let comparisonResponses: Record<string, GA4ApiResponse> | null = null;
        let comparisonErrors: Record<string, any> = {};

        // Fetch comparison data if comparison is enabled
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          const comparisonPromises = AUDIENCE_TABS.map(async (tab) => {
            try {
              const response = await http.get(`${tab.endpoint}/${projectId}/`, {
                params: {
                  start_date: comparisonStartDate,
                  end_date: comparisonEndDate,
                },
                useAuth: true,
              });
              return {
                key: tab.key,
                data: response.data as GA4ApiResponse,
                error: null,
              };
            } catch (tabError) {
              const errorResult = processApiErrorEnhanced(tabError);
              logError(errorResult, `Audience Comparison Data - ${tab.title}`);

              // Return error info instead of throwing to allow partial data loading
              return { key: tab.key, data: null, error: errorResult };
            }
          });

          const comparisonResults = await Promise.all(comparisonPromises);
          comparisonResponses = {};

          comparisonResults.forEach(({ key, data, error }) => {
            if (data) {
              comparisonResponses![key] = data;
            } else if (error) {
              comparisonErrors[key] = error;
            }
          });
        }

        // Check if we have any primary data
        if (Object.keys(primaryResponses).length === 0) {
          // No primary data available, throw the first error or a generic error
          const firstError = Object.values(primaryErrors)[0];
          if (firstError) {
            const error = new Error(firstError.userMessage);
            (error as any).code = firstError.code;
            (error as any).severity = firstError.severity;
            throw error;
          } else {
            throw new Error("No audience data available");
          }
        }

        // Log warnings for any missing data
        const missingPrimary = AUDIENCE_TABS.filter(
          (tab) => !primaryResponses[tab.key]
        );
        const missingComparison = isComparisonEnabled
          ? AUDIENCE_TABS.filter((tab) => !comparisonResponses?.[tab.key])
          : [];

        if (missingPrimary.length > 0) {
          console.warn(
            `Missing primary data for: ${missingPrimary
              .map((t) => t.title)
              .join(", ")}`
          );
        }

        if (missingComparison.length > 0) {
          console.warn(
            `Missing comparison data for: ${missingComparison
              .map((t) => t.title)
              .join(", ")}`
          );
        }

        // Transform the data using the utility function
        return transformAudienceData(
          activeTab,
          primaryResponses,
          comparisonResponses
        );
      } catch (error) {
        const errorResult = processApiErrorEnhanced(error);
        logError(errorResult, "Audience Data Fetch");

        // Re-throw with enhanced error information
        const enhancedError = new Error(errorResult.userMessage);
        (enhancedError as any).code = errorResult.code;
        (enhancedError as any).severity = errorResult.severity;
        (enhancedError as any).shouldRetry = errorResult.shouldRetry;
        (enhancedError as any).technicalMessage = errorResult.technicalMessage;

        throw enhancedError;
      }
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 5,
    retry: (failureCount, error) => {
      // Don't retry on server errors (5xx) or client errors (4xx)
      const status = (error as any)?.response?.status || (error as any)?.status;
      const errorCode = (error as any)?.code;

      if (status >= 400) {
        console.log(`[Audience] Not retrying due to HTTP ${status} error`);
        return false;
      }

      // Don't retry on network errors that are likely persistent
      if (errorCode === "NETWORK_ERROR" || errorCode === "ECONNABORTED") {
        console.log(`[Audience] Not retrying due to ${errorCode} error`);
        return false;
      }

      // Only retry unknown errors, max 1 time
      if (failureCount < 1) {
        console.log(`[Audience] Retrying attempt ${failureCount + 1}`);
        return true;
      }

      return false;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
  });
};

export default useAudience;
