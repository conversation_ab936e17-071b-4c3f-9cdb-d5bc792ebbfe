"use client";
import React, { useEffect, useRef, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import GSCLineChart from "../../_components/line-chart/LineChart";
import LineChartCard from "../../../overview/_components/LineChartCard";
import ChartPopup from "../../../overview/(overview)/audience-overview/_components/ChartPopup";
import { LineChartCards } from "./Audience.types";
import useAudience from "./Audience.hook";
import NoData from "../../_components/NoData";
import ErrorDisplay from "../../_components/ErrorDisplay";

/* ============================== FRAMER MOTION ============================= */
import { AnimatePresence, motion } from "framer-motion";

/* ================================= LODASH ================================= */
import isEqual from "lodash.isequal";

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";
import LineChartSkeleton from "../../../_components/line-chart-skeleton/LineChartSkeleton";
import SmallChartSkeleton from "../../../_components/small-chart-skeleton/SmallChartSkeleton";

/* ================================= ZUSTAND ================================ */
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";

/* ========================================================================== */
const Audience = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const { data, isLoading, isPending, isError, error, refetch } =
    useAudience(activeTab);

  // Chart popup functionality
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  const prevCardTabsRef = useRef<CardTabType[] | null>(data?.cardTabs ?? null);
  const [cardsDataChanged, setCardsDataChanged] = useState(false);

  useEffect(() => {
    if (data?.cardTabs && !isEqual(prevCardTabsRef.current, data.cardTabs)) {
      prevCardTabsRef.current = data.cardTabs;
      setCardsDataChanged(true);
    } else {
      setCardsDataChanged(false);
    }
  }, [data?.cardTabs]);

  useEffect(() => {
    if (data && activeTab === "") {
      setActiveTab(data.cardTabs[0].title);
    }
  }, [data, activeTab]);

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */
  const handleSetChartData = (chart: LineChartCards) => {
    // Transform the chart data to match the expected format
    const transformedData = chart.data.map((item) => ({
      name: item.name,
      value: item.value,
      // Add comparison value if available (this would come from the API response)
      comparisonValue: (item as any).comparisonValue, // Support comparison data if available
    }));

    // Check if we have comparison data
    const hasComparison = transformedData.some(
      (item) => item.comparisonValue !== undefined
    );

    setChartData({
      title: chart.title,
      bigNumber: chart.bigNumber,
      smallNumber: chart.smallNumber,
      data: transformedData,
      hasComparison: hasComparison,
    });
    show();
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Handle error state first - but only show error display for critical errors
  if (isError) {
    const errorCode = error?.code;
    const errorStatus = error?.response?.status || error?.status;

    // Don't show error display for 404 (not found) - just show no data instead
    if (errorCode === "not_found" || errorStatus === 404) {
      return <NoData title="Audience" />;
    }

    // Show error display for other errors (500, network issues, auth, etc.)
    return (
      <ErrorDisplay
        error={error}
        onRetry={() => refetch()}
        title="Audience Data"
        className="min-h-[400px]"
      />
    );
  }

  // Handle no data state (when not loading and no error)
  if (!data && !isLoading && !isPending) {
    return <NoData title="Audience" />;
  }

  // Main render - loading or data available
  return (
    <>
      <Card className="space-y-2">
        <div>
          <Title>Audience</Title>
        </div>
        <DateRange />
        <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap min-h-[74px]">
          {cardsDataChanged
            ? Array.from({ length: 7 }).map((_, i) => (
                <CardTab key={i} isLoading />
              ))
            : prevCardTabsRef.current &&
              prevCardTabsRef.current.map(
                (
                  {
                    title,
                    changeValue,
                    value,
                  }: { title: string; changeValue: string; value: string },
                  index: number
                ) => (
                  <CardTab
                    key={index}
                    title={title}
                    value={value}
                    changeValue={changeValue}
                    className={`border-2 h-16 ${
                      activeTab === title
                        ? "border-primary"
                        : "border-transparent"
                    }`}
                    style={
                      activeTab === title
                        ? { borderColor: themeColor }
                        : { borderColor: "transparent" }
                    }
                    onSelect={() => setActiveTab(title)}
                  />
                )
              )}
        </div>
        <div
          className="mt-8 min-h-[310px] overflow-hidden"
          style={{ position: "relative" }}
        >
          {isLoading || isPending ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <LineChartSkeleton />
            </motion.div>
          ) : data ? (
            <motion.div
              key="data"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <GSCLineChart
                lineChartData={data.lineChartData}
                colors={data.colors}
                selectedLines={data.selectedLines}
                cardsData={data.cardsData}
              />
            </motion.div>
          ) : null}
        </div>
        <div className="flex flex-col lg:flex-row w-full mt-8 lg:gap-2 px-8 min-h-[170px]">
          {isLoading || isPending
            ? Array.from({ length: 3 }).map((_, i) => (
                <motion.div
                  className="w-full"
                  key={`loading-${i}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <SmallChartSkeleton className="w-full" />
                </motion.div>
              ))
            : data &&
              data.lineChartCards.map((item: LineChartCards, index: number) => (
                <motion.div
                  key={`card-${index}`}
                  className="w-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <LineChartCard
                    data={item.data}
                    bigNumber={item.bigNumber}
                    smallNumber={item.smallNumber}
                    title={item.title}
                    className="w-full cursor-pointer"
                    onClick={() => handleSetChartData(item)}
                  />
                </motion.div>
              ))}
        </div>
      </Card>
      <ChartPopup />
    </>
  );
};

export default Audience;
