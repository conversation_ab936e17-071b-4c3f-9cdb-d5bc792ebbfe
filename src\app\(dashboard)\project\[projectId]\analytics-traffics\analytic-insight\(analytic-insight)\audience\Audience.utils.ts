import type {
  GA4ApiResponse,
  GA4DailyMetric,
  GA4Totals,
  AudienceTabConfig,
  AudienceResponse,
  LineChartCards,
} from "./Audience.types";
import type {
  CardTabType,
  LineChartPoint,
  ColorConfig,
  CardsData,
} from "../../../types/AnalyticsTraffics.types";

/**
 * Formats a number to a readable string with appropriate suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

/**
 * Formats percentage to display with % symbol
 */
export const formatPercentage = (rate: number): string => {
  return (rate * 100).toFixed(1) + "%";
};

/**
 * Formats time in seconds to readable format
 */
export const formatTime = (seconds: number): string => {
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${Math.floor(seconds)}s`;
};

/**
 * Formats date from YYYY-MM-DD to readable format
 */
export const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between current and previous period
 */
export const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return "+0%";
  const growth = ((current - previous) / previous) * 100;
  const sign = growth >= 0 ? "+" : "";
  return `${sign}${growth.toFixed(1)}%`;
};

/**
 * Formats value based on type
 */
export const formatValue = (
  value: number,
  type: "number" | "percentage" | "time"
): string => {
  switch (type) {
    case "number":
      return formatNumber(value);
    case "percentage":
      return formatPercentage(value);
    case "time":
      return formatTime(value);
    default:
      return value.toString();
  }
};

/**
 * Configuration for audience tabs
 */
export const AUDIENCE_TABS: AudienceTabConfig[] = [
  {
    key: "total-users",
    title: "Total Users",
    endpoint: "/api/project/GA4/traffic/overview/total-users",
    primaryMetric: "total_users",
    chartMetric: "total_users",
    formatType: "number",
  },
  {
    key: "new-users",
    title: "New Users",
    endpoint: "/api/project/GA4/new-users",
    primaryMetric: "new_users",
    chartMetric: "new_users",
    formatType: "number",
  },
  {
    key: "returning-users",
    title: "Returning Users",
    endpoint: "/api/project/GA4/returning-users",
    primaryMetric: "returning_users",
    chartMetric: "returning_users",
    formatType: "number",
  },
];

/**
 * Default colors for the charts
 */
export const DEFAULT_COLORS: ColorConfig[] = [
  { name: "Total Users", color: "#3B82F6" },
  { name: "New Users", color: "#10B981" },
  { name: "Returning Users", color: "#F59E0B" },
];

/**
 * Transforms GA4 API responses into the format expected by the Audience component
 */
export const transformAudienceData = (
  activeTab: string,
  primaryResponses: Record<string, GA4ApiResponse>,
  comparisonResponses?: Record<string, GA4ApiResponse> | null
): AudienceResponse => {
  const hasComparison = !!comparisonResponses;

  // Create card tabs data
  const cardTabs: CardTabType[] = AUDIENCE_TABS.map((tab) => {
    const primaryData = primaryResponses[tab.key];
    const comparisonData = comparisonResponses?.[tab.key];

    if (!primaryData) {
      return {
        title: tab.title,
        value: "0",
        changeValue: "+0%",
      };
    }

    const primaryValue = primaryData.data.totals[tab.primaryMetric] || 0;
    const comparisonValue = comparisonData?.data.totals[tab.primaryMetric] || 0;

    return {
      title: tab.title,
      value: formatValue(primaryValue, tab.formatType),
      changeValue: hasComparison
        ? calculateGrowth(primaryValue, comparisonValue)
        : "+0%",
    };
  });

  // Find the active tab configuration
  const activeTabConfig = AUDIENCE_TABS.find((tab) => tab.title === activeTab);
  const activeTabKey = activeTabConfig?.key || AUDIENCE_TABS[0].key;
  const activeResponse = primaryResponses[activeTabKey];
  const activeComparisonResponse = comparisonResponses?.[activeTabKey];

  if (!activeResponse) {
    // Return empty data structure if no active response
    return {
      cardTabs,
      lineChartData: [],
      colors: DEFAULT_COLORS,
      selectedLines: [],
      cardsData: {},
      lineChartCards: [],
    };
  }

  // Create line chart data for the main chart
  const lineChartData: LineChartPoint[] = activeResponse.data.daily_metrics.map(
    (metric, index) => {
      const comparisonMetric =
        activeComparisonResponse?.data.daily_metrics[index];
      const chartMetric = activeTabConfig?.chartMetric || "total_users";

      return {
        name: formatDate(metric.date),
        clicks: metric[chartMetric] || 0,
        impressions: comparisonMetric?.[chartMetric] || 0,
      };
    }
  );

  // Create colors configuration
  const colors: ColorConfig[] = [
    { name: "clicks", color: "#3B82F6" },
    ...(hasComparison ? [{ name: "impressions", color: "#10B981" }] : []),
  ];

  // Selected lines for the chart
  const selectedLines = hasComparison ? ["clicks", "impressions"] : ["clicks"];

  // Cards data for the main chart tooltip
  const cardsData: CardsData = {
    clicks: {
      amount:
        activeResponse.data.totals[
          activeTabConfig?.primaryMetric || "total_users"
        ] || 0,
      growth:
        hasComparison && activeComparisonResponse
          ? calculateGrowth(
              activeResponse.data.totals[
                activeTabConfig?.primaryMetric || "total_users"
              ] || 0,
              activeComparisonResponse.data.totals[
                activeTabConfig?.primaryMetric || "total_users"
              ] || 0
            )
          : "+0%",
    },
    ...(hasComparison && activeComparisonResponse
      ? {
          impressions: {
            amount:
              activeComparisonResponse.data.totals[
                activeTabConfig?.primaryMetric || "total_users"
              ] || 0,
            growth: "+0%", // Comparison period doesn't have its own growth
          },
        }
      : {}),
  };

  // Create small chart cards data
  const lineChartCards: LineChartCards[] = AUDIENCE_TABS.map((tab) => {
    const tabResponse = primaryResponses[tab.key];
    const tabComparisonResponse = comparisonResponses?.[tab.key];

    if (!tabResponse) {
      return {
        title: tab.title,
        bigNumber: "0",
        smallNumber: "+0%",
        data: [],
      };
    }

    const primaryValue = tabResponse.data.totals[tab.primaryMetric] || 0;
    const comparisonValue =
      tabComparisonResponse?.data.totals[tab.primaryMetric] || 0;

    // Create chart data for the small cards
    const chartData = tabResponse.data.daily_metrics.map((metric, index) => {
      const comparisonMetric = tabComparisonResponse?.data.daily_metrics[index];
      return {
        name: formatDate(metric.date),
        value: metric[tab.chartMetric] || 0,
        comparisonValue: comparisonMetric?.[tab.chartMetric],
      };
    });

    return {
      title: tab.title,
      bigNumber: formatValue(primaryValue, tab.formatType),
      smallNumber: hasComparison
        ? calculateGrowth(primaryValue, comparisonValue)
        : "+0%",
      data: chartData,
    };
  });

  return {
    cardTabs,
    lineChartData,
    colors,
    selectedLines,
    cardsData,
    lineChartCards,
  };
};
