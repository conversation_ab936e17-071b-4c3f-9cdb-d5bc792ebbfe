import type {
  CardTabType,
  LineChartPoint,
  ColorConfig,
  CardsData,
} from "../../../types/AnalyticsTraffics.types";

export type SelectedLines = string[];

export type LineChartCardsData = {
  name: string;
  value: number;
  comparisonValue?: number;
};

export type LineChartCards = {
  title: string;
  bigNumber: string;
  smallNumber: string;
  data: LineChartCardsData[];
};

export type AudienceResponse = {
  cardTabs: CardTabType[];
  lineChartData: LineChartPoint[];
  colors: ColorConfig[];
  selectedLines: SelectedLines;
  cardsData: CardsData;
  lineChartCards: LineChartCards[];
};

export type ActiveTab = {
  height: number;
  left: number;
  width: number;
  top: number;
};

/* ======================= GA4 API RESPONSE TYPES ======================= */
export interface GA4DailyMetric {
  date: string;
  total_users?: number;
  new_users?: number;
  returning_users?: number;
  views?: number;
  sessions?: number;
  engaged_sessions?: number;
  avg_engagement_time?: number;
  engagement_rate?: number;
  event_count?: number;
  conversions?: number;
  active_users?: number;
}

export interface GA4Totals {
  total_users?: number;
  new_users?: number;
  returning_users?: number;
  views?: number;
  sessions?: number;
  engaged_sessions?: number;
  avg_engagement_time?: number;
  engagement_rate?: number;
  event_count?: number;
  conversions?: number;
  active_users?: number;
  active_users_rate?: number;
  returning_users_rate?: number;
}

export interface GA4Period {
  start_date: string;
  end_date: string;
  days_count: number;
}

export interface GA4ApiResponse {
  status: string;
  project_id: string;
  data: {
    daily_metrics: GA4DailyMetric[];
    totals: GA4Totals;
    period: GA4Period;
  };
  last_sync: string;
}

/* ======================= AUDIENCE TAB CONFIGURATION ======================= */
export interface AudienceTabConfig {
  key: string;
  title: string;
  endpoint: string;
  primaryMetric: keyof GA4Totals;
  chartMetric: keyof GA4DailyMetric;
  formatType: "number" | "percentage" | "time";
}
